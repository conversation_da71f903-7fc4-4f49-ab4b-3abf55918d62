/**
 * 增强资源依赖管理器
 * 提供更高效的资源依赖管理功能
 */
import { EventEmitter } from '../utils/EventEmitter';

/**
 * 依赖类型
 */
export enum DependencyType {
  /** 强依赖（必须加载） */
  STRONG = 'strong',
  /** 弱依赖（可选加载） */
  WEAK = 'weak',
  /** 延迟依赖（按需加载） */
  LAZY = 'lazy',
  /** 预加载依赖（提前加载） */
  PRELOAD = 'preload'
}

/**
 * 依赖信息
 */
export interface DependencyInfo {
  /** 依赖资源ID */
  id: string;
  /** 依赖类型 */
  type: DependencyType;
  /** 依赖优先级 */
  priority?: number;
  /** 依赖元数据 */
  metadata?: Record<string, any>;
}

/**
 * 依赖关系图节点
 */
interface DependencyNode {
  /** 资源ID */
  id: string;
  /** 依赖列表 */
  dependencies: DependencyInfo[];
  /** 被依赖列表 */
  dependents: string[];
  /** 节点元数据 */
  metadata?: Record<string, any>;
}

/**
 * 增强资源依赖管理器选项
 */
export interface EnhancedResourceDependencyManagerOptions {
  /** 是否启用循环依赖检测 */
  enableCycleDetection?: boolean;
  /** 是否启用依赖优化 */
  enableOptimization?: boolean;
  /** 是否启用自动优化 */
  enableAutoOptimization?: boolean;
  /** 是否启用深度分析 */
  enableDeepAnalysis?: boolean;
  /** 是否启用依赖缓存 */
  enableDependencyCache?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 增强资源依赖管理器
 */
export class EnhancedResourceDependencyManager extends EventEmitter {
  /** 依赖图 */
  private dependencyGraph: Map<string, DependencyNode> = new Map();

  /** 是否启用循环依赖检测 */
  private enableCycleDetection: boolean;

  /** 是否启用依赖优化 */
  private enableOptimization: boolean;

  /** 是否启用自动优化 */
  private enableAutoOptimization: boolean;

  /** 是否启用深度分析 */
  private enableDeepAnalysis: boolean;

  /** 是否启用依赖缓存 */
  private enableDependencyCache: boolean;

  /** 是否启用调试模式 */
  private debug: boolean;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 依赖分析缓存 */
  private analysisCache: Map<string, any> = new Map();

  /** 循环依赖缓存 */
  private cyclicDependenciesCache: string[][] = [];

  /** 上次优化时间 */
  private lastOptimizationTime: number = 0;

  /** 优化间隔（毫秒） */
  private optimizationInterval: number = 60000; // 默认1分钟

  /**
   * 创建增强资源依赖管理器实例
   * @param options 依赖管理器选项
   */
  constructor(options: EnhancedResourceDependencyManagerOptions = {}) {
    super();

    this.enableCycleDetection = options.enableCycleDetection !== undefined ? options.enableCycleDetection : true;
    this.enableOptimization = options.enableOptimization !== undefined ? options.enableOptimization : true;
    this.enableAutoOptimization = options.enableAutoOptimization !== undefined ? options.enableAutoOptimization : false;
    this.enableDeepAnalysis = options.enableDeepAnalysis !== undefined ? options.enableDeepAnalysis : false;
    this.enableDependencyCache = options.enableDependencyCache !== undefined ? options.enableDependencyCache : true;
    this.debug = options.debug !== undefined ? options.debug : false;
  }

  /**
   * 初始化依赖管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 如果启用自动优化，则设置定时优化
    if (this.enableAutoOptimization) {
      this.setupAutoOptimization();
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 设置自动优化
   */
  private setupAutoOptimization(): void {
    // 设置定时器，定期优化依赖关系
    const intervalId = setInterval(() => {
      const now = Date.now();

      // 检查是否达到优化间隔
      if (now - this.lastOptimizationTime >= this.optimizationInterval) {
        this.optimizeDependencies();
        this.lastOptimizationTime = now;
      }
    }, Math.min(this.optimizationInterval, 10000)); // 最多10秒检查一次

    // 添加销毁时清理定时器
    this.once('disposed', () => {
      clearInterval(intervalId);
    });
  }

  /**
   * 设置优化间隔
   * @param interval 优化间隔（毫秒）
   */
  public setOptimizationInterval(interval: number): void {
    this.optimizationInterval = Math.max(1000, interval); // 最小1秒
  }

  /**
   * 添加依赖关系
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @param type 依赖类型
   * @param priority 依赖优先级
   * @param metadata 依赖元数据
   * @returns 是否成功添加
   */
  public addDependency(
    resourceId: string,
    dependencyId: string,
    type: DependencyType = DependencyType.STRONG,
    priority: number = 0,
    metadata?: Record<string, any>
  ): boolean {
    // 如果资源ID和依赖资源ID相同，则忽略
    if (resourceId === dependencyId) {
      if (this.debug) {
        console.warn(`[DependencyManager] 忽略自依赖: ${resourceId}`);
      }
      return false;
    }

    // 如果启用循环依赖检测，则检查是否会形成循环依赖
    if (this.enableCycleDetection && this.wouldFormCycle(resourceId, dependencyId)) {
      if (this.debug) {
        console.warn(`[DependencyManager] 检测到循环依赖: ${resourceId} -> ${dependencyId}`);
      }
      return false;
    }

    // 获取或创建资源节点
    let resourceNode = this.getOrCreateNode(resourceId);
    let dependencyNode = this.getOrCreateNode(dependencyId);

    // 检查是否已存在相同依赖
    const existingDep = resourceNode.dependencies.find(dep => dep.id === dependencyId);

    if (existingDep) {
      // 如果已存在，则更新依赖类型（强依赖优先）
      if (this.shouldUpdateDependencyType(existingDep.type, type)) {
        existingDep.type = type;
      }

      // 更新优先级（取最高值）
      if (priority > (existingDep.priority || 0)) {
        existingDep.priority = priority;
      }

      // 更新元数据
      if (metadata) {
        existingDep.metadata = { ...existingDep.metadata, ...metadata };
      }

      // 发出依赖更新事件
      this.emit('dependencyUpdated', { resourceId, dependencyId, type, priority, metadata });
    } else {
      // 添加新依赖
      resourceNode.dependencies.push({
        id: dependencyId,
        type,
        priority,
        metadata
      });

      // 更新被依赖关系
      if (!dependencyNode.dependents.includes(resourceId)) {
        dependencyNode.dependents.push(resourceId);
      }

      // 发出依赖添加事件
      this.emit('dependencyAdded', { resourceId, dependencyId, type, priority, metadata });
    }

    return true;
  }

  /**
   * 判断是否应该更新依赖类型
   * @param existingType 现有依赖类型
   * @param newType 新依赖类型
   * @returns 是否应该更新
   */
  private shouldUpdateDependencyType(existingType: DependencyType, newType: DependencyType): boolean {
    // 依赖类型优先级: STRONG > PRELOAD > LAZY > WEAK
    const typePriority = {
      [DependencyType.STRONG]: 3,
      [DependencyType.PRELOAD]: 2,
      [DependencyType.LAZY]: 1,
      [DependencyType.WEAK]: 0
    };

    return typePriority[newType] > typePriority[existingType];
  }

  /**
   * 获取或创建依赖图节点
   * @param id 资源ID
   * @returns 依赖图节点
   */
  private getOrCreateNode(id: string): DependencyNode {
    let node = this.dependencyGraph.get(id);

    if (!node) {
      node = {
        id,
        dependencies: [],
        dependents: []
      };
      this.dependencyGraph.set(id, node);
    }

    return node;
  }

  /**
   * 检查添加依赖是否会形成循环
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @returns 是否会形成循环
   */
  private wouldFormCycle(resourceId: string, dependencyId: string): boolean {
    // 如果依赖资源不存在，则不会形成循环
    if (!this.dependencyGraph.has(dependencyId)) {
      return false;
    }

    // 使用深度优先搜索检查是否存在从依赖资源到资源的路径
    const visited = new Set<string>();

    const dfs = (currentId: string): boolean => {
      // 如果找到了资源ID，则存在循环
      if (currentId === resourceId) {
        return true;
      }

      // 标记为已访问
      visited.add(currentId);

      // 获取当前节点
      const node = this.dependencyGraph.get(currentId);

      if (!node) {
        return false;
      }

      // 遍历所有依赖
      for (const dependent of node.dependents) {
        // 如果已访问过，则跳过
        if (visited.has(dependent)) {
          continue;
        }

        // 递归检查
        if (dfs(dependent)) {
          return true;
        }
      }

      return false;
    };

    return dfs(dependencyId);
  }

  /**
   * 移除依赖关系
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @returns 是否成功移除
   */
  public removeDependency(resourceId: string, dependencyId: string): boolean {
    // 获取资源节点
    const resourceNode = this.dependencyGraph.get(resourceId);
    const dependencyNode = this.dependencyGraph.get(dependencyId);

    if (!resourceNode || !dependencyNode) {
      return false;
    }

    // 查找依赖索引
    const index = resourceNode.dependencies.findIndex(dep => dep.id === dependencyId);

    if (index === -1) {
      return false;
    }

    // 移除依赖
    resourceNode.dependencies.splice(index, 1);

    // 更新被依赖关系
    const depIndex = dependencyNode.dependents.indexOf(resourceId);
    if (depIndex !== -1) {
      dependencyNode.dependents.splice(depIndex, 1);
    }

    // 发出依赖移除事件
    this.emit('dependencyRemoved', { resourceId, dependencyId });

    return true;
  }

  /**
   * 获取资源的直接依赖
   * @param resourceId 资源ID
   * @returns 依赖信息数组
   */
  public getDependencies(resourceId: string): DependencyInfo[] {
    const node = this.dependencyGraph.get(resourceId);
    return node ? [...node.dependencies] : [];
  }

  /**
   * 获取资源的直接被依赖
   * @param resourceId 资源ID
   * @returns 资源ID数组
   */
  public getDependents(resourceId: string): string[] {
    const node = this.dependencyGraph.get(resourceId);
    return node ? [...node.dependents] : [];
  }

  /**
   * 获取特定类型的依赖
   * @param resourceId 资源ID
   * @param type 依赖类型
   * @returns 依赖信息数组
   */
  public getDependenciesByType(resourceId: string, type: DependencyType): DependencyInfo[] {
    const dependencies = this.getDependencies(resourceId);
    return dependencies.filter(dep => dep.type === type);
  }

  /**
   * 获取强依赖
   * @param resourceId 资源ID
   * @returns 依赖信息数组
   */
  public getStrongDependencies(resourceId: string): DependencyInfo[] {
    return this.getDependenciesByType(resourceId, DependencyType.STRONG);
  }

  /**
   * 获取弱依赖
   * @param resourceId 资源ID
   * @returns 依赖信息数组
   */
  public getWeakDependencies(resourceId: string): DependencyInfo[] {
    return this.getDependenciesByType(resourceId, DependencyType.WEAK);
  }

  /**
   * 获取延迟依赖
   * @param resourceId 资源ID
   * @returns 依赖信息数组
   */
  public getLazyDependencies(resourceId: string): DependencyInfo[] {
    return this.getDependenciesByType(resourceId, DependencyType.LAZY);
  }

  /**
   * 获取预加载依赖
   * @param resourceId 资源ID
   * @returns 依赖信息数组
   */
  public getPreloadDependencies(resourceId: string): DependencyInfo[] {
    return this.getDependenciesByType(resourceId, DependencyType.PRELOAD);
  }

  /**
   * 获取资源的所有依赖（包括间接依赖）
   * @param resourceId 资源ID
   * @param types 包含的依赖类型数组
   * @returns 依赖信息数组
   */
  public getAllDependencies(
    resourceId: string,
    types: DependencyType[] = [DependencyType.STRONG, DependencyType.WEAK, DependencyType.LAZY, DependencyType.PRELOAD]
  ): DependencyInfo[] {
    const result: DependencyInfo[] = [];
    const visited = new Set<string>();

    // 递归获取依赖
    const collectDependencies = (id: string) => {
      // 如果已访问过，则跳过
      if (visited.has(id)) {
        return;
      }

      // 标记为已访问
      visited.add(id);

      // 获取直接依赖
      const dependencies = this.getDependencies(id);

      for (const dep of dependencies) {
        // 如果依赖类型不在指定类型中，则跳过
        if (!types.includes(dep.type)) {
          continue;
        }

        // 添加依赖
        if (!result.some(existingDep => existingDep.id === dep.id)) {
          result.push({ ...dep });
        }

        // 递归获取依赖的依赖
        collectDependencies(dep.id);
      }
    };

    collectDependencies(resourceId);

    return result;
  }

  /**
   * 获取资源的所有被依赖（包括间接被依赖）
   * @param resourceId 资源ID
   * @returns 资源ID数组
   */
  public getAllDependents(resourceId: string): string[] {
    const result: string[] = [];
    const visited = new Set<string>();

    // 递归获取被依赖
    const collectDependents = (id: string) => {
      // 如果已访问过，则跳过
      if (visited.has(id)) {
        return;
      }

      // 标记为已访问
      visited.add(id);

      // 获取直接被依赖
      const dependents = this.getDependents(id);

      for (const depId of dependents) {
        // 添加被依赖
        if (!result.includes(depId)) {
          result.push(depId);
        }

        // 递归获取被依赖的被依赖
        collectDependents(depId);
      }
    };

    collectDependents(resourceId);

    return result;
  }

  /**
   * 获取加载顺序
   * @param resourceIds 资源ID数组
   * @param includeTypes 包含的依赖类型数组
   * @returns 按加载顺序排序的资源ID数组
   */
  public getLoadingOrder(
    resourceIds: string[],
    includeTypes: DependencyType[] = [DependencyType.STRONG, DependencyType.PRELOAD]
  ): string[] {
    // 使用拓扑排序获取加载顺序
    const result: string[] = [];
    const visited = new Set<string>();
    const temp = new Set<string>();

    // 递归拓扑排序
    const visit = (id: string): boolean => {
      // 如果已在临时集合中，则存在循环依赖
      if (temp.has(id)) {
        if (this.debug) {
          console.warn(`[DependencyManager] 检测到循环依赖: ${id}`);
        }
        return false;
      }

      // 如果已访问过，则跳过
      if (visited.has(id)) {
        return true;
      }

      // 添加到临时集合
      temp.add(id);

      // 获取依赖
      const dependencies = this.getDependencies(id)
        .filter(dep => includeTypes.includes(dep.type));

      // 按优先级排序依赖
      dependencies.sort((a, b) => (b.priority || 0) - (a.priority || 0));

      // 递归访问依赖
      for (const dep of dependencies) {
        if (!visit(dep.id)) {
          return false;
        }
      }

      // 从临时集合中移除
      temp.delete(id);

      // 添加到已访问集合
      visited.add(id);

      // 添加到结果
      result.unshift(id);

      return true;
    };

    // 遍历所有资源
    for (const id of resourceIds) {
      if (!visit(id)) {
        // 如果存在循环依赖，则使用备用方法
        if (this.debug) {
          console.warn(`[DependencyManager] 使用备用方法获取加载顺序`);
        }
        return this.getLoadingOrderFallback(resourceIds, includeTypes);
      }
    }

    return result;
  }

  /**
   * 获取加载顺序（备用方法）
   * @param resourceIds 资源ID数组
   * @param includeTypes 包含的依赖类型数组
   * @returns 按加载顺序排序的资源ID数组
   */
  private getLoadingOrderFallback(
    resourceIds: string[],
    includeTypes: DependencyType[] = [DependencyType.STRONG, DependencyType.PRELOAD]
  ): string[] {
    // 计算每个资源的依赖数量
    const dependencyCounts = new Map<string, number>();

    for (const id of resourceIds) {
      // 获取所有依赖
      const allDeps = this.getAllDependencies(id, includeTypes);
      dependencyCounts.set(id, allDeps.length);
    }

    // 按依赖数量排序（依赖越少越先加载）
    return [...resourceIds].sort((a, b) => {
      const countA = dependencyCounts.get(a) || 0;
      const countB = dependencyCounts.get(b) || 0;
      return countA - countB;
    });
  }

  /**
   * 优化依赖关系
   * @param strategies 优化策略数组，为空则使用所有策略
   * @returns 是否成功优化
   */
  public optimizeDependencies(strategies?: string[]): boolean {
    if (!this.enableOptimization) {
      return false;
    }

    if (this.debug) {
      console.log(`[DependencyManager] 开始优化依赖关系`);
    }

    const startTime = Date.now();

    // 默认使用所有策略
    const allStrategies = ['removeRedundant', 'mergeSimilar', 'convertWeak', 'optimizeLazy', 'optimizePreload'];
    const strategiesToUse = strategies || allStrategies;

    // 应用选定的优化策略
    if (strategiesToUse.includes('removeRedundant')) {
      this.removeRedundantDependencies();
    }

    if (strategiesToUse.includes('mergeSimilar')) {
      this.mergeSimilarDependencies();
    }

    if (strategiesToUse.includes('convertWeak')) {
      this.convertToWeakDependencies();
    }

    if (strategiesToUse.includes('optimizeLazy')) {
      this.optimizeLazyDependencies();
    }

    if (strategiesToUse.includes('optimizePreload')) {
      this.optimizePreloadDependencies();
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    if (this.debug) {
      console.log(`[DependencyManager] 依赖优化完成，耗时: ${duration}ms`);
    }

    // 更新上次优化时间
    this.lastOptimizationTime = endTime;

    // 发出依赖优化事件
    this.emit('dependenciesOptimized', {
      strategies: strategiesToUse,
      duration,
      timestamp: endTime
    });

    return true;
  }

  /**
   * 移除冗余依赖
   */
  private removeRedundantDependencies(): void {
    // 遍历所有节点
    for (const [id, node] of this.dependencyGraph.entries()) {
      // 获取直接依赖
      const directDeps = new Set(node.dependencies.map(dep => dep.id));

      // 要移除的冗余依赖
      const redundantDeps: string[] = [];

      // 检查每个直接依赖
      for (const depInfo of node.dependencies) {
        const depId = depInfo.id;

        // 获取该依赖的所有依赖
        const subDeps = this.getAllDependencies(depId);

        // 检查是否存在冗余
        for (const subDep of subDeps) {
          if (directDeps.has(subDep.id)) {
            redundantDeps.push(subDep.id);
          }
        }
      }

      // 移除冗余依赖
      for (const redundantDep of redundantDeps) {
        this.removeDependency(id, redundantDep);

        if (this.debug) {
          console.log(`[DependencyManager] 移除冗余依赖: ${id} -> ${redundantDep}`);
        }
      }
    }
  }

  /**
   * 合并相似依赖
   */
  private mergeSimilarDependencies(): void {
    // 遍历所有节点
    for (const [id, node] of this.dependencyGraph.entries()) {
      // 按依赖ID分组
      const depGroups = new Map<string, DependencyInfo[]>();

      for (const dep of node.dependencies) {
        const deps = depGroups.get(dep.id) || [];
        deps.push(dep);
        depGroups.set(dep.id, deps);
      }

      // 合并每个组中的依赖
      for (const [depId, deps] of depGroups.entries()) {
        if (deps.length <= 1) {
          continue;
        }

        // 找出最高优先级的依赖类型
        let bestType = deps[0].type;
        let bestPriority = deps[0].priority || 0;
        let mergedMetadata: Record<string, any> = { ...deps[0].metadata };

        for (let i = 1; i < deps.length; i++) {
          const dep = deps[i];

          // 更新最佳类型
          if (this.shouldUpdateDependencyType(bestType, dep.type)) {
            bestType = dep.type;
          }

          // 更新最高优先级
          if ((dep.priority || 0) > bestPriority) {
            bestPriority = dep.priority || 0;
          }

          // 合并元数据
          if (dep.metadata) {
            mergedMetadata = { ...mergedMetadata, ...dep.metadata };
          }
        }

        // 移除所有旧依赖
        for (let i = 0; i < deps.length; i++) {
          this.removeDependency(id, depId);
        }

        // 添加合并后的依赖
        this.addDependency(id, depId, bestType, bestPriority, mergedMetadata);

        if (this.debug) {
          console.log(`[DependencyManager] 合并依赖: ${id} -> ${depId}, 类型: ${bestType}, 优先级: ${bestPriority}`);
        }
      }
    }
  }

  /**
   * 转换为弱依赖
   * 将可能的强依赖转换为弱依赖，以减少加载时间
   */
  private convertToWeakDependencies(): void {
    // 遍历所有节点
    for (const [id, node] of this.dependencyGraph.entries()) {
      // 获取所有强依赖
      const strongDeps = node.dependencies.filter(dep => dep.type === DependencyType.STRONG);

      // 检查每个强依赖
      for (const depInfo of strongDeps) {
        const depId = depInfo.id;

        // 检查是否有其他资源也依赖这个资源
        const otherDependents = this.getDependents(depId).filter(d => d !== id);

        // 如果有其他资源依赖这个资源，且这个资源不是关键资源，则可以考虑转换为弱依赖
        if (otherDependents.length > 0 && !this.isResourceCritical(depId)) {
          // 更新依赖类型
          this.updateDependencyType(id, depId, DependencyType.WEAK);

          if (this.debug) {
            console.log(`[DependencyManager] 转换为弱依赖: ${id} -> ${depId}`);
          }
        }
      }
    }
  }

  /**
   * 优化延迟依赖
   * 将不常用的依赖转换为延迟依赖
   */
  private optimizeLazyDependencies(): void {
    // 遍历所有节点
    for (const [id, node] of this.dependencyGraph.entries()) {
      // 获取所有强依赖和弱依赖
      const nonLazyDeps = node.dependencies.filter(dep =>
        dep.type === DependencyType.STRONG || dep.type === DependencyType.WEAK
      );

      // 检查每个依赖
      for (const depInfo of nonLazyDeps) {
        const depId = depInfo.id;

        // 检查是否是不常用的资源
        if (this.isResourceRarelyUsed(depId)) {
          // 更新依赖类型
          this.updateDependencyType(id, depId, DependencyType.LAZY);

          if (this.debug) {
            console.log(`[DependencyManager] 转换为延迟依赖: ${id} -> ${depId}`);
          }
        }
      }
    }
  }

  /**
   * 优化预加载依赖
   * 将频繁使用的依赖转换为预加载依赖
   */
  private optimizePreloadDependencies(): void {
    // 遍历所有节点
    for (const [id, node] of this.dependencyGraph.entries()) {
      // 获取所有非预加载依赖
      const nonPreloadDeps = node.dependencies.filter(dep => dep.type !== DependencyType.PRELOAD);

      // 检查每个依赖
      for (const depInfo of nonPreloadDeps) {
        const depId = depInfo.id;

        // 检查是否是频繁使用的资源
        if (this.isResourceFrequentlyUsed(depId)) {
          // 更新依赖类型
          this.updateDependencyType(id, depId, DependencyType.PRELOAD);

          if (this.debug) {
            console.log(`[DependencyManager] 转换为预加载依赖: ${id} -> ${depId}`);
          }
        }
      }
    }
  }

  /**
   * 更新依赖类型
   * @param resourceId 资源ID
   * @param dependencyId 依赖资源ID
   * @param newType 新依赖类型
   * @returns 是否成功更新
   */
  private updateDependencyType(resourceId: string, dependencyId: string, newType: DependencyType): boolean {
    // 获取资源节点
    const resourceNode = this.dependencyGraph.get(resourceId);

    if (!resourceNode) {
      return false;
    }

    // 查找依赖
    const depIndex = resourceNode.dependencies.findIndex(dep => dep.id === dependencyId);

    if (depIndex === -1) {
      return false;
    }

    // 获取旧依赖信息
    const oldDep = resourceNode.dependencies[depIndex];
    const oldType = oldDep.type;

    // 如果类型相同，则不需要更新
    if (oldType === newType) {
      return false;
    }

    // 更新依赖类型
    resourceNode.dependencies[depIndex] = {
      ...oldDep,
      type: newType
    };

    // 发出依赖更新事件
    this.emit('dependencyUpdated', {
      resourceId,
      dependencyId,
      type: newType,
      priority: oldDep.priority,
      metadata: oldDep.metadata,
      oldType
    });

    return true;
  }

  /**
   * 检查资源是否关键
   * @param resourceId 资源ID
   * @returns 是否关键
   */
  private isResourceCritical(resourceId: string): boolean {
    // 在实际项目中，应该根据资源的使用情况和重要性来判断
    // 这里使用简单的启发式方法：如果被多个资源强依赖，则认为是关键资源

    // 获取所有依赖这个资源的资源
    const dependents = this.getDependents(resourceId);

    // 计算强依赖的数量
    let strongDependentsCount = 0;

    for (const depId of dependents) {
      const depNode = this.dependencyGraph.get(depId);

      if (depNode) {
        const dep = depNode.dependencies.find(d => d.id === resourceId);

        if (dep && dep.type === DependencyType.STRONG) {
          strongDependentsCount++;
        }
      }
    }

    // 如果有多个强依赖，则认为是关键资源
    return strongDependentsCount > 1;
  }

  /**
   * 检查资源是否不常用
   * @param resourceId 资源ID
   * @returns 是否不常用
   */
  private isResourceRarelyUsed(resourceId: string): boolean {
    // 在实际项目中，应该根据资源的使用频率和访问时间来判断
    // 这里使用简单的启发式方法：如果只被一个资源依赖，则认为是不常用资源

    // 获取所有依赖这个资源的资源
    const dependents = this.getDependents(resourceId);

    // 如果只有一个依赖，则认为是不常用资源
    return dependents.length <= 1;
  }

  /**
   * 检查资源是否频繁使用
   * @param resourceId 资源ID
   * @returns 是否频繁使用
   */
  private isResourceFrequentlyUsed(resourceId: string): boolean {
    // 在实际项目中，应该根据资源的使用频率和访问时间来判断
    // 这里使用简单的启发式方法：如果被多个资源依赖，则认为是频繁使用资源

    // 获取所有依赖这个资源的资源
    const dependents = this.getDependents(resourceId);

    // 如果有多个依赖，则认为是频繁使用资源
    return dependents.length > 2;
  }

  /**
   * 分析依赖关系
   * @param resourceId 资源ID，如果为空则分析所有资源
   * @returns 分析结果
   */
  public analyzeDependencies(resourceId?: string): any {
    // 如果启用缓存且缓存中有结果，则直接返回
    if (this.enableDependencyCache && resourceId && this.analysisCache.has(resourceId)) {
      return this.analysisCache.get(resourceId);
    }

    const startTime = Date.now();

    // 要分析的资源ID数组
    const resourceIds = resourceId ? [resourceId] : Array.from(this.dependencyGraph.keys());

    // 分析结果
    const result: any = {
      // 循环依赖
      circularDependencies: this.detectCircularDependencies(resourceIds),
      // 未使用资源
      unusedResources: this.detectUnusedResources(),
      // 依赖链
      dependencyChains: resourceId ? this.calculateDependencyChains(resourceId) : [],
      // 加载顺序
      loadOrder: this.getLoadingOrder(resourceIds),
      // 依赖统计
      statistics: this.calculateDependencyStatistics(resourceIds),
      // 分析时间
      analysisTime: Date.now() - startTime
    };

    // 如果启用深度分析，则进行更多分析
    if (this.enableDeepAnalysis) {
      result.redundantDependencies = this.detectRedundantDependencies(resourceIds);
      result.weakDependencyCandidates = this.detectWeakDependencyCandidates(resourceIds);
      result.lazyDependencyCandidates = this.detectLazyDependencyCandidates(resourceIds);
      result.preloadDependencyCandidates = this.detectPreloadDependencyCandidates(resourceIds);
    }

    // 如果启用缓存，则缓存结果
    if (this.enableDependencyCache && resourceId) {
      this.analysisCache.set(resourceId, result);
    }

    return result;
  }

  /**
   * 检测循环依赖
   * @param resourceIds 资源ID数组
   * @returns 循环依赖数组
   */
  private detectCircularDependencies(resourceIds: string[]): string[][] {
    // 如果已经缓存了循环依赖，则直接返回
    if (this.cyclicDependenciesCache.length > 0) {
      return this.cyclicDependenciesCache;
    }

    const cycles: string[][] = [];
    const visited = new Set<string>();
    const path: string[] = [];

    // 深度优先搜索检测循环依赖
    const dfs = (id: string): void => {
      // 如果已在路径中，则找到循环
      if (path.includes(id)) {
        const cycleStart = path.indexOf(id);
        const cycle = path.slice(cycleStart).concat(id);

        // 检查是否已存在相同循环
        const cycleStr = cycle.sort().join(',');
        if (!cycles.some(c => c.sort().join(',') === cycleStr)) {
          cycles.push(cycle);
        }

        return;
      }

      // 如果已访问过，则跳过
      if (visited.has(id)) {
        return;
      }

      // 标记为已访问
      visited.add(id);

      // 添加到路径
      path.push(id);

      // 获取依赖
      const node = this.dependencyGraph.get(id);

      if (node) {
        // 递归处理依赖
        for (const dep of node.dependencies) {
          dfs(dep.id);
        }
      }

      // 从路径中移除
      path.pop();
    };

    // 对每个资源进行DFS
    for (const id of resourceIds) {
      dfs(id);
    }

    // 缓存结果
    this.cyclicDependenciesCache = cycles;

    return cycles;
  }

  /**
   * 检测未使用资源
   * @returns 未使用资源ID数组
   */
  private detectUnusedResources(): string[] {
    const allResources = Array.from(this.dependencyGraph.keys());
    const usedResources = new Set<string>();

    // 收集所有被依赖的资源
    for (const [id, node] of this.dependencyGraph.entries()) {
      for (const dep of node.dependencies) {
        usedResources.add(dep.id);
      }
    }

    // 找出没有被依赖的资源
    return allResources.filter(id => !usedResources.has(id));
  }

  /**
   * 计算依赖链
   * @param resourceId 资源ID
   * @returns 依赖链数组
   */
  private calculateDependencyChains(resourceId: string): string[][] {
    const chains: string[][] = [];
    const visited = new Set<string>();

    // 递归构建依赖链
    const buildChain = (id: string, chain: string[] = []): void => {
      // 添加当前资源到链中
      chain.push(id);

      // 获取依赖
      const node = this.dependencyGraph.get(id);

      if (!node || node.dependencies.length === 0) {
        // 如果没有依赖，则保存链
        chains.push([...chain]);
      } else {
        // 递归处理依赖
        for (const dep of node.dependencies) {
          // 避免循环依赖
          if (!chain.includes(dep.id)) {
            buildChain(dep.id, [...chain]);
          }
        }
      }
    };

    // 从指定资源开始构建依赖链
    buildChain(resourceId);

    return chains;
  }

  /**
   * 计算依赖统计信息
   * @param resourceIds 资源ID数组
   * @returns 统计信息
   */
  private calculateDependencyStatistics(resourceIds: string[]): any {
    const statistics: any = {
      totalResources: resourceIds.length,
      totalDependencies: 0,
      dependencyTypes: {
        [DependencyType.STRONG]: 0,
        [DependencyType.WEAK]: 0,
        [DependencyType.LAZY]: 0,
        [DependencyType.PRELOAD]: 0
      },
      maxDependencyChainLength: 0,
      averageDependencyChainLength: 0,
      resourcesWithMostDependencies: [],
      resourcesWithMostDependents: []
    };

    // 计算总依赖数和各类型依赖数
    for (const id of resourceIds) {
      const node = this.dependencyGraph.get(id);

      if (node) {
        statistics.totalDependencies += node.dependencies.length;

        // 统计各类型依赖数
        for (const dep of node.dependencies) {
          statistics.dependencyTypes[dep.type]++;
        }
      }
    }

    // 计算最长依赖链长度和平均依赖链长度
    let totalChainLength = 0;
    let chainCount = 0;

    for (const id of resourceIds) {
      const chains = this.calculateDependencyChains(id);

      for (const chain of chains) {
        statistics.maxDependencyChainLength = Math.max(statistics.maxDependencyChainLength, chain.length);
        totalChainLength += chain.length;
        chainCount++;
      }
    }

    if (chainCount > 0) {
      statistics.averageDependencyChainLength = totalChainLength / chainCount;
    }

    // 找出依赖最多的资源
    const resourcesByDependencyCount = [...resourceIds]
      .map(id => ({
        id,
        count: this.getDependencies(id).length
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    statistics.resourcesWithMostDependencies = resourcesByDependencyCount;

    // 找出被依赖最多的资源
    const resourcesByDependentCount = [...resourceIds]
      .map(id => ({
        id,
        count: this.getDependents(id).length
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    statistics.resourcesWithMostDependents = resourcesByDependentCount;

    return statistics;
  }

  /**
   * 检测冗余依赖
   * @param resourceIds 资源ID数组
   * @returns 冗余依赖数组
   */
  private detectRedundantDependencies(resourceIds: string[]): any[] {
    const redundantDeps: any[] = [];

    // 遍历所有资源
    for (const id of resourceIds) {
      const node = this.dependencyGraph.get(id);

      if (!node) continue;

      // 获取直接依赖
      const directDeps = new Set(node.dependencies.map(dep => dep.id));

      // 检查每个直接依赖
      for (const depInfo of node.dependencies) {
        const depId = depInfo.id;

        // 获取该依赖的所有依赖
        const subDeps = this.getAllDependencies(depId);

        // 检查是否存在冗余
        for (const subDep of subDeps) {
          if (directDeps.has(subDep.id) && subDep.id !== depId) {
            redundantDeps.push({
              resourceId: id,
              redundantDependencyId: subDep.id,
              throughDependencyId: depId
            });
          }
        }
      }
    }

    return redundantDeps;
  }

  /**
   * 检测可转换为弱依赖的候选项
   * @param resourceIds 资源ID数组
   * @returns 弱依赖候选项数组
   */
  private detectWeakDependencyCandidates(resourceIds: string[]): any[] {
    const candidates: any[] = [];

    // 遍历所有资源
    for (const id of resourceIds) {
      const node = this.dependencyGraph.get(id);

      if (!node) continue;

      // 获取所有强依赖
      const strongDeps = node.dependencies.filter(dep => dep.type === DependencyType.STRONG);

      // 检查每个强依赖
      for (const depInfo of strongDeps) {
        const depId = depInfo.id;

        // 检查是否有其他资源也依赖这个资源
        const otherDependents = this.getDependents(depId).filter(d => d !== id);

        // 如果有其他资源依赖这个资源，且这个资源不是关键资源，则可以考虑转换为弱依赖
        if (otherDependents.length > 0 && !this.isResourceCritical(depId)) {
          candidates.push({
            resourceId: id,
            dependencyId: depId,
            currentType: DependencyType.STRONG,
            suggestedType: DependencyType.WEAK,
            reason: '该资源被多个其他资源依赖，可以考虑使用弱依赖'
          });
        }
      }
    }

    return candidates;
  }

  /**
   * 检测可转换为延迟依赖的候选项
   * @param resourceIds 资源ID数组
   * @returns 延迟依赖候选项数组
   */
  private detectLazyDependencyCandidates(resourceIds: string[]): any[] {
    const candidates: any[] = [];

    // 遍历所有资源
    for (const id of resourceIds) {
      const node = this.dependencyGraph.get(id);

      if (!node) continue;

      // 获取所有强依赖和弱依赖
      const nonLazyDeps = node.dependencies.filter(dep =>
        dep.type === DependencyType.STRONG || dep.type === DependencyType.WEAK
      );

      // 检查每个依赖
      for (const depInfo of nonLazyDeps) {
        const depId = depInfo.id;

        // 检查是否是不常用的资源
        if (this.isResourceRarelyUsed(depId)) {
          candidates.push({
            resourceId: id,
            dependencyId: depId,
            currentType: depInfo.type,
            suggestedType: DependencyType.LAZY,
            reason: '该资源不常用，可以考虑使用延迟依赖'
          });
        }
      }
    }

    return candidates;
  }

  /**
   * 检测可转换为预加载依赖的候选项
   * @param resourceIds 资源ID数组
   * @returns 预加载依赖候选项数组
   */
  private detectPreloadDependencyCandidates(resourceIds: string[]): any[] {
    const candidates: any[] = [];

    // 遍历所有资源
    for (const id of resourceIds) {
      const node = this.dependencyGraph.get(id);

      if (!node) continue;

      // 获取所有非预加载依赖
      const nonPreloadDeps = node.dependencies.filter(dep => dep.type !== DependencyType.PRELOAD);

      // 检查每个依赖
      for (const depInfo of nonPreloadDeps) {
        const depId = depInfo.id;

        // 检查是否是频繁使用的资源
        if (this.isResourceFrequentlyUsed(depId)) {
          candidates.push({
            resourceId: id,
            dependencyId: depId,
            currentType: depInfo.type,
            suggestedType: DependencyType.PRELOAD,
            reason: '该资源频繁使用，可以考虑使用预加载依赖'
          });
        }
      }
    }

    return candidates;
  }

  /**
   * 清除分析缓存
   */
  public clearAnalysisCache(): void {
    this.analysisCache.clear();
    this.cyclicDependenciesCache = [];
  }

  /**
   * 清除所有依赖关系
   */
  public clearDependencies(): void {
    this.dependencyGraph.clear();
    this.clearAnalysisCache();
    this.emit('dependenciesCleared');
  }

  /**
   * 销毁依赖管理器
   */
  public dispose(): void {
    this.clearDependencies();
    this.removeAllListeners();
    this.initialized = false;
    this.emit('disposed');
  }
